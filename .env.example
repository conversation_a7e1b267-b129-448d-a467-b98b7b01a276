# MongoDB Connection
MONGODB_URI=***************************************************************************

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-change-this-in-production

# Fazpass API (for OTP authentication)
FAZPASS_API_URL=https://api.fazpass.com
FAZPASS_MERCHANT_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZGVudGlmaWVyIjo4MTMzfQ.84_oH8neqSC3XVq6myN5HvdlAq8iCMwsrSjayrMJZi4
FAZPASS_GATEWAY_KEY=b7d8b898-1a55-484b-a6c6-80af02e618f2

# Midtrans Payment Gateway
MIDTRANS_CLIENT_KEY=your-midtrans-client-key
MIDTRANS_SERVER_KEY=your-midtrans-server-key
MIDTRANS_MERCHANT_ID=your-midtrans-merchant-id
MIDTRANS_IS_PRODUCTION=false

# DigitalOcean Spaces Configuration (for profile photos)
DO_SPACES_ENDPOINT=ams3.digitaloceanspaces.com
DO_SPACES_BUCKET=pairsona
DO_SPACES_ACCESS_KEY=DO801BGJ92H6HHPXJA3G
DO_SPACES_SECRET_KEY=2Fr+SxwRjgZbLheYWX5JJPPTj+unZ5bHlbQmwhAQ8Qw
